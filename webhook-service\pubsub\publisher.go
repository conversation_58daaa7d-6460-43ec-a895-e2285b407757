package pubsub

import (
	"context"
	"encoding/json"
	"log"

	dapr "github.com/dapr/go-sdk/client"
)

const (
	PubSubName = "redisstreams-pubsub"
	TopicName  = "webhooks"
)

type Publisher struct {
	daprClient dapr.Client
}

type WebhookEvent struct {
	DeliveryID string                 `json:"delivery_id"`
	Timestamp  string                 `json:"timestamp"`
	Source     string                 `json:"source"`
	Payload    map[string]interface{} `json:"payload"`
}

func NewPublisher(daprClient dapr.Client) *Publisher {
	return &Publisher{
		daprClient: daprClient,
	}
}

// PublishWebhookEvent publishes a webhook event to the Dapr pubsub
func (p *Publisher) PublishWebhookEvent(ctx context.Context, event WebhookEvent) error {
	eventData, err := json.Marshal(event)
	if err != nil {
		log.Printf("Error marshaling webhook event: %v", err)
		return err
	}

	err = p.daprClient.PublishEvent(ctx, PubSubName, TopicName, eventData)
	if err != nil {
		log.Printf("Error publishing webhook event: %v", err)
		return err
	}

	log.Printf("Successfully published webhook event with delivery ID: %s", event.DeliveryID)
	return nil
}
