version: '3.8'

services:
  # Redis for Dapr state store and pubsub
  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - dapr-network

  # Webhook Service
  webhook-service:
    build:
      context: ./webhook-service
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - redis
    networks:
      - dapr-network
    environment:
      - DAPR_HTTP_PORT=3500
      - DAPR_GRPC_PORT=50001

  # Webhook Service Dapr Sidecar
  webhook-service-dapr:
    image: "daprio/daprd:1.12.0"
    command: [
      "./daprd",
      "-app-id", "webhook-service",
      "-app-port", "8080",
      "-dapr-http-port", "3500",
      "-dapr-grpc-port", "50001",
      "-components-path", "/components",
      "-log-level", "info"
    ]
    volumes:
      - "./components:/components"
    depends_on:
      - webhook-service
      - redis
    network_mode: "service:webhook-service"

  # Processor Service
  processor-service:
    build:
      context: ./processor-service
      dockerfile: Dockerfile
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - dapr-network
    environment:
      - DAPR_HTTP_PORT=3501
      - DAPR_GRPC_PORT=50002

  # Processor Service Dapr Sidecar
  processor-service-dapr:
    image: "daprio/daprd:1.12.0"
    command: [
      "./daprd",
      "-app-id", "processor-service",
      "-app-port", "8081",
      "-dapr-http-port", "3501",
      "-dapr-grpc-port", "50002",
      "-components-path", "/components",
      "-log-level", "info"
    ]
    volumes:
      - "./components:/components"
    depends_on:
      - processor-service
      - redis
    network_mode: "service:processor-service"

volumes:
  redis_data:

networks:
  dapr-network:
    driver: bridge
