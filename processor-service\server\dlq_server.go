package server

import (
	"log"
	"net/http"
	"strings"

	"processor-service/handlers"
)

// DLQServer provides HTTP endpoints for DLQ management
type DLQServer struct {
	dlqHandlers *handlers.DLQHandlers
	mux         *http.ServeMux
}

// NewDLQServer creates a new DLQ HTTP server
func NewDLQServer(dlqHandlers *handlers.DLQHandlers) *DLQServer {
	mux := http.NewServeMux()
	server := &DLQServer{
		dlqHandlers: dlqHandlers,
		mux:         mux,
	}

	server.setupRoutes()
	return server
}

// setupRoutes configures the HTTP routes for DLQ management
func (s *DLQServer) setupRoutes() {
	// DLQ management routes
	s.mux.HandleFunc("/dlq", s.handleDLQRoutes)
	s.mux.HandleFunc("/dlq/", s.handleDLQRoutes)
	
	// Health check for DLQ server
	s.mux.HandleFunc("/dlq-health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("DLQ Server OK"))
	})
}

// handleDLQRoutes routes DLQ requests to appropriate handlers
func (s *DLQServer) handleDLQRoutes(w http.ResponseWriter, r *http.Request) {
	path := r.URL.Path

	switch {
	case path == "/dlq" && r.Method == "GET":
		// GET /dlq - list all DLQ messages
		s.dlqHandlers.ListDLQMessages(w, r)
		
	case path == "/dlq/stats" && r.Method == "GET":
		// GET /dlq/stats - get DLQ statistics
		s.dlqHandlers.DLQStats(w, r)
		
	case strings.HasPrefix(path, "/dlq/") && !strings.Contains(path[5:], "/"):
		// GET /dlq/{deliveryId} - get specific DLQ message
		// DELETE /dlq/{deliveryId} - delete specific DLQ message
		if r.Method == "GET" {
			s.dlqHandlers.GetDLQMessage(w, r)
		} else if r.Method == "DELETE" {
			s.dlqHandlers.DeleteDLQMessage(w, r)
		} else {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
		
	case strings.HasSuffix(path, "/reprocess") && r.Method == "POST":
		// POST /dlq/{deliveryId}/reprocess - reprocess DLQ message
		s.dlqHandlers.ReprocessDLQMessage(w, r)
		
	default:
		http.Error(w, "Not found", http.StatusNotFound)
	}
}

// Start starts the DLQ HTTP server on the specified port
func (s *DLQServer) Start(port string) error {
	log.Printf("Starting DLQ management server on port %s", port)
	log.Printf("DLQ endpoints:")
	log.Printf("  GET    %s/dlq           - List all DLQ messages", port)
	log.Printf("  GET    %s/dlq/stats     - Get DLQ statistics", port)
	log.Printf("  GET    %s/dlq/{id}      - Get specific DLQ message", port)
	log.Printf("  DELETE %s/dlq/{id}      - Delete DLQ message", port)
	log.Printf("  POST   %s/dlq/{id}/reprocess - Reprocess DLQ message", port)
	
	return http.ListenAndServe(port, s.mux)
}
