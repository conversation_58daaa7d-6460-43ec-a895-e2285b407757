package dedup

import (
	"context"
	"fmt"
	"log"
	"time"

	dapr "github.com/dapr/go-sdk/client"
)

const (
	StateStoreName = "redis"
	TTLSeconds     = 300 // 5 minutes TTL for deduplication
)

type Deduplicator struct {
	daprClient dapr.Client
}

func NewDeduplicator(daprClient dapr.Client) *Deduplicator {
	return &Deduplicator{
		daprClient: daprClient,
	}
}

// IsDuplicate checks if the delivery ID has been seen before
func (d *Deduplicator) IsDuplicate(ctx context.Context, deliveryID string) (bool, error) {
	if deliveryID == "" {
		return false, fmt.Errorf("delivery ID cannot be empty")
	}

	key := fmt.Sprintf("webhook_delivery_%s", deliveryID)
	
	// Try to get the existing value
	item, err := d.daprClient.GetState(ctx, StateStoreName, key, nil)
	if err != nil {
		log.Printf("Error checking duplicate for delivery ID %s: %v", deliveryID, err)
		return false, err
	}

	// If item exists, it's a duplicate
	if len(item.Value) > 0 {
		log.Printf("Duplicate delivery detected: %s", deliveryID)
		return true, nil
	}

	// Save the delivery ID with TTL
	metadata := map[string]string{
		"ttlInSeconds": fmt.Sprintf("%d", TTLSeconds),
	}

	err = d.daprClient.SaveState(ctx, StateStoreName, key, []byte(time.Now().Format(time.RFC3339)), metadata)
	if err != nil {
		log.Printf("Error saving delivery ID %s: %v", deliveryID, err)
		return false, err
	}

	log.Printf("New delivery ID saved: %s", deliveryID)
	return false, nil
}
