package main

import (
	"context"
	"log"
	"net/http"

	"github.com/dapr/go-sdk/service/common"
	daprd "github.com/dapr/go-sdk/service/http"
	"processor-service/handlers"
)

const (
	ServerPort = ":8081"
	PubSubName = "redisstreams-pubsub"
	TopicName  = "webhooks"
)

func main() {
	log.Println("Starting Processor Service...")

	// Create event handler
	eventHandler := handlers.NewEventHandler()

	// Create Dapr service
	s := daprd.NewService(ServerPort)

	// Add topic subscription
	if err := s.AddTopicEventHandler(&common.Subscription{
		PubsubName: PubSubName,
		Topic:      TopicName,
		Route:      "/events/webhook",
	}, eventHandler.HandleWebhookEvent); err != nil {
		log.Fatalf("Error adding topic event handler: %v", err)
	}

	// Add HTTP endpoint for direct calls
	if err := s.AddServiceInvocationHandler("/events/webhook", eventHandler.HandleWebhookHTTP); err != nil {
		log.Fatalf("Error adding service invocation handler: %v", err)
	}

	// Add health check endpoint
	if err := s.AddServiceInvocationHandler("/health", func(ctx context.Context, in *common.InvocationEvent) (out *common.Content, err error) {
		return &common.Content{
			Data:        []byte("OK"),
			ContentType: "text/plain",
		}, nil
	}); err != nil {
		log.Fatalf("Error adding health check handler: %v", err)
	}

	log.Printf("Processor service listening on %s", ServerPort)
	log.Printf("Subscribed to topic '%s' on pubsub '%s'", TopicName, PubSubName)

	// Start the service
	if err := s.Start(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Error starting service: %v", err)
	}
}
