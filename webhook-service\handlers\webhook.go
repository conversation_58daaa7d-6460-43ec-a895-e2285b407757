package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	dapr "github.com/dapr/go-sdk/client"
	"webhook-service/dedup"
	"webhook-service/pubsub"
)

type WebhookHandler struct {
	deduplicator *dedup.Deduplicator
	publisher    *pubsub.Publisher
}

type WebhookPayload struct {
	Action     string                 `json:"action"`
	Repository map[string]interface{} `json:"repository,omitempty"`
	Issue      map[string]interface{} `json:"issue,omitempty"`
	Data       map[string]interface{} `json:"data,omitempty"`
}

func NewWebhookHandler(daprClient dapr.Client) *WebhookHandler {
	return &WebhookHandler{
		deduplicator: dedup.NewDeduplicator(daprClient),
		publisher:    pubsub.NewPublisher(daprClient),
	}
}

func (h *WebhookHandler) HandleWebhook(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get delivery ID from header
	deliveryID := r.Header.Get("X-Delivery-ID")
	if deliveryID == "" {
		http.Error(w, "X-Delivery-ID header is required", http.StatusBadRequest)
		return
	}

	// Check for duplicates
	isDuplicate, err := h.deduplicator.IsDuplicate(r.Context(), deliveryID)
	if err != nil {
		log.Printf("Error checking duplicate: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if isDuplicate {
		log.Printf("Duplicate webhook received with delivery ID: %s", deliveryID)
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"status": "duplicate", "delivery_id": "%s"}`, deliveryID)
		return
	}

	// Parse JSON payload
	var payload WebhookPayload
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		log.Printf("Error parsing webhook payload: %v", err)
		http.Error(w, "Invalid JSON payload", http.StatusBadRequest)
		return
	}

	// Basic validation
	if payload.Action == "" {
		http.Error(w, "Action field is required", http.StatusBadRequest)
		return
	}

	// Create webhook event
	event := pubsub.WebhookEvent{
		DeliveryID: deliveryID,
		Timestamp:  time.Now().Format(time.RFC3339),
		Source:     r.Header.Get("User-Agent"),
		Payload:    map[string]interface{}{
			"action":     payload.Action,
			"repository": payload.Repository,
			"issue":      payload.Issue,
			"data":       payload.Data,
		},
	}

	// Publish event
	if err := h.publisher.PublishWebhookEvent(r.Context(), event); err != nil {
		log.Printf("Error publishing webhook event: %v", err)
		http.Error(w, "Failed to publish event", http.StatusInternalServerError)
		return
	}

	// Success response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"status": "success", "delivery_id": "%s", "timestamp": "%s"}`, 
		deliveryID, event.Timestamp)
}
