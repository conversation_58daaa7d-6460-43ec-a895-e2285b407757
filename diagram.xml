<svg aria-roledescription="sequence" role="graphics-document document" viewBox="-50 -10 2452.5 2756" style="max-width: 2452.5px;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92"><g><rect class="actor actor-bottom" ry="3" rx="3" name="PS" height="65" width="169" stroke="#666" fill="#eaeaea" y="2670" x="2054.5"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2702.5" x="2139"><tspan dy="-8" x="2139">⚙️ Processor Service</tspan></text><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2702.5" x="2139"><tspan dy="8" x="2139">(Port 8081)</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="PD" height="65" width="209" stroke="#666" fill="#eaeaea" y="2670" x="1740.5"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2702.5" x="1845"><tspan dy="-8" x="1845">🔧 Processor Dapr Sidecar</tspan></text><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2702.5" x="1845"><tspan dy="8" x="1845">(Port 3501)</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="Redis" height="65" width="150" stroke="#666" fill="#eaeaea" y="2670" x="1414"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2702.5" x="1489"><tspan dy="-8" x="1489">🗄️ Redis</tspan></text><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2702.5" x="1489"><tspan dy="8" x="1489">(State + PubSub)</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="WHD" height="65" width="209" stroke="#666" fill="#eaeaea" y="2670" x="935.5"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2702.5" x="1040"><tspan dy="-8" x="1040">🔧 Webhook Dapr Sidecar</tspan></text><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2702.5" x="1040"><tspan dy="8" x="1040">(Port 3500)</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="WH" height="65" width="168" stroke="#666" fill="#eaeaea" y="2670" x="557"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2702.5" x="641"><tspan dy="-8" x="641">🎯 Webhook Service</tspan></text><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2702.5" x="641"><tspan dy="8" x="641">(Port 8080)</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="Client" height="65" width="210" stroke="#666" fill="#eaeaea" y="2670" x="0"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="2702.5" x="105"><tspan dy="0" x="105">🌐 Client (curl/PowerShell)</tspan></text></g><g><line name="PS" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2670" x2="2139" y1="65" x1="2139" id="actor17"></line><g id="root-17"><rect class="actor actor-top" ry="3" rx="3" name="PS" height="65" width="169" stroke="#666" fill="#eaeaea" y="0" x="2054.5"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="2139"><tspan dy="-8" x="2139">⚙️ Processor Service</tspan></text><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="2139"><tspan dy="8" x="2139">(Port 8081)</tspan></text></g></g><g><line name="PD" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2670" x2="1845" y1="65" x1="1845" id="actor16"></line><g id="root-16"><rect class="actor actor-top" ry="3" rx="3" name="PD" height="65" width="209" stroke="#666" fill="#eaeaea" y="0" x="1740.5"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1845"><tspan dy="-8" x="1845">🔧 Processor Dapr Sidecar</tspan></text><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1845"><tspan dy="8" x="1845">(Port 3501)</tspan></text></g></g><g><line name="Redis" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2670" x2="1489" y1="65" x1="1489" id="actor15"></line><g id="root-15"><rect class="actor actor-top" ry="3" rx="3" name="Redis" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="1414"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1489"><tspan dy="-8" x="1489">🗄️ Redis</tspan></text><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1489"><tspan dy="8" x="1489">(State + PubSub)</tspan></text></g></g><g><line name="WHD" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2670" x2="1040" y1="65" x1="1040" id="actor14"></line><g id="root-14"><rect class="actor actor-top" ry="3" rx="3" name="WHD" height="65" width="209" stroke="#666" fill="#eaeaea" y="0" x="935.5"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1040"><tspan dy="-8" x="1040">🔧 Webhook Dapr Sidecar</tspan></text><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1040"><tspan dy="8" x="1040">(Port 3500)</tspan></text></g></g><g><line name="WH" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2670" x2="641" y1="65" x1="641" id="actor13"></line><g id="root-13"><rect class="actor actor-top" ry="3" rx="3" name="WH" height="65" width="168" stroke="#666" fill="#eaeaea" y="0" x="557"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="641"><tspan dy="-8" x="641">🎯 Webhook Service</tspan></text><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="641"><tspan dy="8" x="641">(Port 8080)</tspan></text></g></g><g><line name="Client" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="2670" x2="105" y1="65" x1="105" id="actor12"></line><g id="root-12"><rect class="actor actor-top" ry="3" rx="3" name="Client" height="65" width="210" stroke="#666" fill="#eaeaea" y="0" x="0"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="105"><tspan dy="0" x="105">🌐 Client (curl/PowerShell)</tspan></text></g></g><style>#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .error-icon{fill:#a44141;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .edge-thickness-normal{stroke-width:1px;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .marker.cross{stroke:lightgrey;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 p{margin:0;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .actor{stroke:#ccc;fill:#1f2020;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 text.actor&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .actor-line{stroke:#ccc;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .messageLine0{stroke-width:1.5;stroke-dasharray:none;stroke:lightgrey;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .messageLine1{stroke-width:1.5;stroke-dasharray:2,2;stroke:lightgrey;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 #arrowhead path{fill:lightgrey;stroke:lightgrey;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .sequenceNumber{fill:black;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 #sequencenumber{fill:lightgrey;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 #crosshead path{fill:lightgrey;stroke:lightgrey;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .messageText{fill:lightgrey;stroke:none;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .labelBox{stroke:#ccc;fill:#1f2020;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .labelText,#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .labelText&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .loopText,#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .loopText&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .loopLine{stroke-width:2px;stroke-dasharray:2,2;stroke:#ccc;fill:#ccc;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .note{stroke:hsl(180, 0%, 18.3529411765%);fill:hsl(180, 1.5873015873%, 28.3529411765%);}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .noteText,#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .noteText&gt;tspan{fill:rgb(183.8476190475, 181.5523809523, 181.5523809523);stroke:none;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .activation0{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .activation1{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .activation2{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .actorPopupMenu{position:absolute;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .actorPopupMenuPanel{position:absolute;fill:#1f2020;box-shadow:0px 8px 16px 0px rgba(0,0,0,0.2);filter:drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .actor-man line{stroke:#ccc;fill:#1f2020;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 .actor-man circle,#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 line{stroke:#ccc;fill:#1f2020;stroke-width:2px;}#mermaid-f40d1a2c-350c-4873-9dff-3ebe08e0fd92 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g></g><defs><symbol height="24" width="24" id="computer"><path d="M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z" transform="scale(.5)"></path></symbol></defs><defs><symbol clip-rule="evenodd" fill-rule="evenodd" id="database"><path d="M12.258.001l.256.004.255.005.253.008.251.01.249.012.247.015.246.016.242.019.241.02.239.023.236.024.233.027.231.028.229.031.225.032.223.034.22.036.217.038.214.04.211.041.208.043.205.045.201.046.198.048.194.05.191.051.187.053.183.054.18.056.175.057.172.059.168.06.163.061.16.063.155.064.15.066.074.033.073.033.071.034.07.034.069.035.068.035.067.035.066.035.064.036.064.036.062.036.06.036.06.037.058.037.058.037.055.038.055.038.053.038.052.038.051.039.05.039.048.039.047.039.045.04.044.04.043.04.041.04.04.041.039.041.037.041.036.041.034.041.033.042.032.042.03.042.029.042.027.042.026.043.024.043.023.043.021.043.02.043.018.044.017.043.015.044.013.044.012.044.011.045.009.044.007.045.006.045.004.045.002.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z" transform="scale(.5)"></path></symbol></defs><defs><symbol height="24" width="24" id="clock"><path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z" transform="scale(.5)"></path></symbol></defs><defs><marker orient="auto-start-reverse" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="7.9" id="arrowhead"><path d="M -1 0 L 10 5 L 0 10 z"></path></marker></defs><defs><marker refY="4.5" refX="4" orient="auto" markerHeight="8" markerWidth="15" id="crosshead"><path style="stroke-dasharray: 0, 0;" d="M 1,2 L 6,7 M 6,2 L 1,7" stroke-width="1pt" stroke="#000000" fill="none"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="15.5" id="filled-head"><path d="M 18,7 L9,13 L14,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="40" markerWidth="60" refY="15" refX="15" id="sequencenumber"><circle r="6" cy="15" cx="15"></circle></marker></defs><g><rect class="note" height="39" width="2084" stroke="#666" fill="#EDF2AE" y="75" x="80"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="80" x="1122"><tspan x="1122">1. WEBHOOK RECEPTION &amp; DEDUPLICATION</tspan></text></g><g><rect class="note" height="39" width="471" stroke="#666" fill="#EDF2AE" y="674" x="405.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="679" x="641"><tspan x="641">📝 Log: "New delivery ID saved: test-batch-1-20250612132707"</tspan></text></g><g><rect class="note" height="39" width="2084" stroke="#666" fill="#EDF2AE" y="723" x="80"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="728" x="1122"><tspan x="1122">2. EVENT PUBLISHING</tspan></text></g><g><rect class="note" height="39" width="698" stroke="#666" fill="#EDF2AE" y="992" x="292"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="997" x="641"><tspan x="641">📝 Log: "Successfully published webhook event with delivery ID: test-batch-1-20250612132707"</tspan></text></g><g><rect class="note" height="39" width="2084" stroke="#666" fill="#EDF2AE" y="1111" x="80"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1116" x="1122"><tspan x="1122">3. EVENT CONSUMPTION &amp; PROCESSING</tspan></text></g><g><rect class="note" height="39" width="355" stroke="#666" fill="#EDF2AE" y="1300" x="1961.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1305" x="2139"><tspan x="2139">📝 Log: "Received direct HTTP webhook event"</tspan></text></g><g><rect class="note" height="39" width="423" stroke="#666" fill="#EDF2AE" y="1349" x="1927.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1354" x="2139"><tspan x="2139">📝 Log: "Processing HTTP webhook event - Delivery ID: "</tspan></text></g><g><rect class="note" height="40" width="297" stroke="#666" fill="#EDF2AE" y="1398" x="1990.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1403" x="2139"><tspan x="2139">🎲 Random failure check (25% chance)</tspan></text></g><g><rect class="note" height="39" width="190" stroke="#666" fill="#EDF2AE" y="1493" x="2044"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1498" x="2139"><tspan x="2139">✅ Processing succeeds</tspan></text></g><g><rect class="note" height="39" width="407" stroke="#666" fill="#EDF2AE" y="1542" x="1935.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1547" x="2139"><tspan x="2139">📝 Log: "Successfully processed HTTP webhook event"</tspan></text></g><g><rect class="note" height="39" width="169" stroke="#666" fill="#EDF2AE" y="1736" x="2054.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1741" x="2139"><tspan x="2139">❌ Simulated failure</tspan></text></g><g><rect class="note" height="39" width="323" stroke="#666" fill="#EDF2AE" y="1785" x="1977.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1790" x="2139"><tspan x="2139">📝 Log: "Simulated failure for delivery ID: "</tspan></text></g><g><rect class="note" height="39" width="325" stroke="#666" fill="#EDF2AE" y="1884" x="1682.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1889" x="1845"><tspan x="1845">📝 Log: "retriable error returned from app"</tspan></text></g><g><rect class="note" height="39" width="326" stroke="#666" fill="#EDF2AE" y="1933" x="1682"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1938" x="1845"><tspan x="1845">🔄 Schedule retry with exponential backoff</tspan></text></g><g><line class="loopLine" y2="1448" x2="2352.5" y1="1448" x1="1478"></line><line class="loopLine" y2="2032" x2="2352.5" y1="1448" x1="2352.5"></line><line class="loopLine" y2="2032" x2="2352.5" y1="2032" x1="1478"></line><line class="loopLine" y2="2032" x2="1478" y1="1448" x1="1478"></line><line style="stroke-dasharray: 3, 3;" class="loopLine" y2="1696" x2="2352.5" y1="1696" x1="1478"></line><polygon class="labelBox" points="1478,1448 1528,1448 1528,1461 1519.6,1468 1478,1468"></polygon><text style="font-size: 16px; font-weight: 400;" class="labelText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1461" x="1503">alt</text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="1466" x="1940.25"><tspan x="1940.25">[Success Case (75% probability)]</tspan></text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="1714" x="1915.25">[Failure Case (25% probability)]</text></g><g><rect class="note" height="40" width="2084" stroke="#666" fill="#EDF2AE" y="2042" x="80"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2047" x="1122"><tspan x="1122">4. RETRY MECHANISM (for failed events)</tspan></text></g><g><rect class="note" height="40" width="349" stroke="#666" fill="#EDF2AE" y="2092" x="1670.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2097" x="1845"><tspan x="1845">⏰ Wait for retry interval (exponential backoff)</tspan></text></g><g><rect class="note" height="40" width="407" stroke="#666" fill="#EDF2AE" y="2212" x="1935.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2217" x="2139"><tspan x="2139">📝 Log: "Received direct HTTP webhook event" (again)</tspan></text></g><g><rect class="note" height="40" width="340" stroke="#666" fill="#EDF2AE" y="2262" x="1969"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2267" x="2139"><tspan x="2139">🎲 Random failure check again (25% chance)</tspan></text></g><g><rect class="note" height="39" width="209" stroke="#666" fill="#EDF2AE" y="2457" x="1740.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2462" x="1845"><tspan x="1845">✅ Event finally processed</tspan></text></g><g><rect class="note" height="39" width="209" stroke="#666" fill="#EDF2AE" y="2601" x="1740.5"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2606" x="1845"><tspan x="1845">🔄 Schedule another retry</tspan></text></g><g><line class="loopLine" y2="2312" x2="2150" y1="2312" x1="1478"></line><line class="loopLine" y2="2650" x2="2150" y1="2312" x1="2150"></line><line class="loopLine" y2="2650" x2="2150" y1="2650" x1="1478"></line><line class="loopLine" y2="2650" x2="1478" y1="2312" x1="1478"></line><line style="stroke-dasharray: 3, 3;" class="loopLine" y2="2511" x2="2150" y1="2511" x1="1478"></line><polygon class="labelBox" points="1478,2312 1528,2312 1528,2325 1519.6,2332 1478,2332"></polygon><text style="font-size: 16px; font-weight: 400;" class="labelText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2325" x="1503">alt</text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="2330" x="1839"><tspan x="1839">[Retry Success]</tspan></text><text style="font-size: 16px; font-weight: 400;" class="loopText" text-anchor="middle" y="2529" x="1814">[Retry Failure]</text></g><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="129" x="372">POST /webhook</text><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="148" x="372">X-Delivery-ID: test-batch-1-20250612132707</text><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="167" x="372">{"action": "batch-test-1"}</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="204" x2="637" y1="204" x1="106"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="219" x="839">Check if delivery ID exists</text><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="238" x="839">(Dapr State Store API)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="274" x2="1036" y1="274" x1="642"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="289" x="1263">GET webhook_delivery_test-batch-1-20250612132707</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="324" x2="1485" y1="324" x1="1041"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="339" x="1266">Key not found (new delivery)</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="374" x2="1044" y1="374" x1="1488"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="389" x="842">Not duplicate</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="424" x2="645" y1="424" x1="1039"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="439" x="839">Save delivery ID with TTL</text><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="458" x="839">(Dapr State Store API)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="494" x2="1036" y1="494" x1="642"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="509" x="1263">SET webhook_delivery_test-batch-1-20250612132707</text><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="528" x="1263">TTL: 300 seconds</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="564" x2="1485" y1="564" x1="1041"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="579" x="1266">OK</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="614" x2="1044" y1="614" x1="1488"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="629" x="842">Saved</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="664" x2="645" y1="664" x1="1039"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="777" x="839">Publish event to topic 'webhooks'</text><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="796" x="839">(Dapr PubSub API)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="832" x2="1036" y1="832" x1="642"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="847" x="1263">XADD webhooks * {event_data}</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="882" x2="1485" y1="882" x1="1041"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="897" x="1266">Message ID: 1749727627585-0</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="932" x2="1044" y1="932" x1="1488"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="947" x="842">Published</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="982" x2="645" y1="982" x1="1039"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1046" x="375">200 OK</text><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1065" x="375">{"status": "success", "delivery_id": "test-batch-1-20250612132707"}</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1101" x2="109" y1="1101" x1="640"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1165" x="1666">XREAD webhooks (Redis Streams)</text><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1185" x="1666">Message: 1749727627585-0</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1220" x2="1841" y1="1220" x1="1490"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1235" x="1991">POST /events/webhook</text><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1254" x="1991">{webhook_event_data}</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1290" x2="2135" y1="1290" x1="1846"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1596" x="1994">200 OK {"status": "processed"}</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1631" x2="1849" y1="1631" x1="2138"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1646" x="1669">ACK message 1749727627585-0</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1681" x2="1493" y1="1681" x1="1844"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1839" x="1994">500 Internal Server Error</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1874" x2="1849" y1="1874" x1="2138"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1987" x="1669">NACK message (keep in stream for retry)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2022" x2="1493" y1="2022" x1="1844"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2147" x="1991">POST /events/webhook (RETRY)</text><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2167" x="1991">{same_webhook_event_data}</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2202" x2="2135" y1="2202" x1="1846"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2362" x="1994">200 OK</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2397" x2="1849" y1="2397" x1="2138"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2412" x="1669">ACK message</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2447" x2="1493" y1="2447" x1="1844"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2556" x="1994">500 Error</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2591" x2="1849" y1="2591" x1="2138"></line></svg>