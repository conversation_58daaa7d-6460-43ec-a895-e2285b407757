package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	"processor-service/dlq"
)

// DLQHandlers provides HTTP endpoints for DLQ management
type DLQHandlers struct {
	dlqHandler *dlq.DLQHandler
}

// NewDLQHandlers creates new DLQ HTTP handlers
func NewDLQHandlers(dlqHandler *dlq.DLQHandler) *DLQHandlers {
	return &DLQHandlers{
		dlqHandler: dlqHandler,
	}
}

// ListDLQMessages handles GET /dlq - lists all DLQ messages
func (h *DLQHandlers) ListDLQMessages(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	messages, err := h.dlqHandler.ListDLQMessages(r.Context())
	if err != nil {
		log.Printf("Error listing DLQ messages: %v", err)
		http.Error(w, "Failed to list DLQ messages", http.StatusInternalServerError)
		return
	}

	w.<PERSON>er().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":   "success",
		"messages": messages,
		"count":    len(messages),
	})
}

// GetDLQMessage handles GET /dlq/{deliveryId} - gets a specific DLQ message
func (h *DLQHandlers) GetDLQMessage(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Extract delivery ID from URL path
	path := strings.TrimPrefix(r.URL.Path, "/dlq/")
	if path == "" || path == r.URL.Path {
		http.Error(w, "Delivery ID is required", http.StatusBadRequest)
		return
	}

	deliveryID := path

	message, err := h.dlqHandler.GetDLQMessage(r.Context(), deliveryID)
	if err != nil {
		log.Printf("Error getting DLQ message: %v", err)
		http.Error(w, "Failed to get DLQ message", http.StatusInternalServerError)
		return
	}

	if message == nil {
		http.Error(w, "DLQ message not found", http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":  "success",
		"message": message,
	})
}

// ReprocessDLQMessage handles POST /dlq/{deliveryId}/reprocess - reprocesses a DLQ message
func (h *DLQHandlers) ReprocessDLQMessage(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Extract delivery ID from URL path
	path := strings.TrimPrefix(r.URL.Path, "/dlq/")
	parts := strings.Split(path, "/")
	if len(parts) < 2 || parts[1] != "reprocess" {
		http.Error(w, "Invalid URL format. Use /dlq/{deliveryId}/reprocess", http.StatusBadRequest)
		return
	}

	deliveryID := parts[0]

	err := h.dlqHandler.ReprocessDLQMessage(r.Context(), deliveryID)
	if err != nil {
		log.Printf("Error reprocessing DLQ message: %v", err)
		if strings.Contains(err.Error(), "not found") {
			http.Error(w, "DLQ message not found", http.StatusNotFound)
		} else {
			http.Error(w, "Failed to reprocess DLQ message", http.StatusInternalServerError)
		}
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"status": "success", "message": "DLQ message reprocessed", "delivery_id": "%s"}`, deliveryID)
}

// DeleteDLQMessage handles DELETE /dlq/{deliveryId} - deletes a DLQ message
func (h *DLQHandlers) DeleteDLQMessage(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Extract delivery ID from URL path
	path := strings.TrimPrefix(r.URL.Path, "/dlq/")
	if path == "" || path == r.URL.Path {
		http.Error(w, "Delivery ID is required", http.StatusBadRequest)
		return
	}

	deliveryID := path

	err := h.dlqHandler.DeleteDLQMessage(r.Context(), deliveryID)
	if err != nil {
		log.Printf("Error deleting DLQ message: %v", err)
		if strings.Contains(err.Error(), "not found") {
			http.Error(w, "DLQ message not found", http.StatusNotFound)
		} else {
			http.Error(w, "Failed to delete DLQ message", http.StatusInternalServerError)
		}
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"status": "success", "message": "DLQ message deleted", "delivery_id": "%s"}`, deliveryID)
}

// DLQStats handles GET /dlq/stats - provides DLQ statistics
func (h *DLQHandlers) DLQStats(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	messages, err := h.dlqHandler.ListDLQMessages(r.Context())
	if err != nil {
		log.Printf("Error getting DLQ stats: %v", err)
		http.Error(w, "Failed to get DLQ stats", http.StatusInternalServerError)
		return
	}

	stats := map[string]interface{}{
		"total_messages": len(messages),
		"status":         "success",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}
