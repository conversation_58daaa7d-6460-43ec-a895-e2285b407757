#!/bin/bash

# Test script for webhook microservice

WEBHOOK_URL="http://localhost:8080/webhook"
HEALTH_URL_WEBHOOK="http://localhost:8080/health"
HEALTH_URL_PROCESSOR="http://localhost:8081/health"

echo "🧪 Testing Webhook Microservice"
echo "================================"

# Check if services are running
echo "📋 Checking service health..."
curl -s $HEALTH_URL_WEBHOOK > /dev/null && echo "✅ Webhook service is healthy" || echo "❌ Webhook service is not responding"
curl -s $HEALTH_URL_PROCESSOR > /dev/null && echo "✅ Processor service is healthy" || echo "❌ Processor service is not responding"
echo ""

# Test 1: Send a valid webhook
echo "🔄 Test 1: Sending valid webhook..."
DELIVERY_ID="test-$(date +%s)"
RESPONSE=$(curl -s -X POST $WEBHOOK_URL \
  -H "Content-Type: application/json" \
  -H "X-Delivery-ID: $DELIVERY_ID" \
  -d '{
    "action": "opened",
    "repository": {
      "name": "test-repo",
      "full_name": "user/test-repo"
    },
    "issue": {
      "number": 1,
      "title": "Test Issue"
    }
  }')
echo "Response: $RESPONSE"
echo ""

# Test 2: Test deduplication
echo "🔄 Test 2: Testing deduplication (same delivery ID)..."
RESPONSE=$(curl -s -X POST $WEBHOOK_URL \
  -H "Content-Type: application/json" \
  -H "X-Delivery-ID: $DELIVERY_ID" \
  -d '{
    "action": "opened",
    "repository": {
      "name": "test-repo"
    }
  }')
echo "Response: $RESPONSE"
echo ""

# Test 3: Missing delivery ID
echo "🔄 Test 3: Testing missing delivery ID (should fail)..."
RESPONSE=$(curl -s -w "HTTP %{http_code}" -X POST $WEBHOOK_URL \
  -H "Content-Type: application/json" \
  -d '{"action": "test"}')
echo "Response: $RESPONSE"
echo ""

# Test 4: Invalid JSON
echo "🔄 Test 4: Testing invalid JSON (should fail)..."
RESPONSE=$(curl -s -w "HTTP %{http_code}" -X POST $WEBHOOK_URL \
  -H "Content-Type: application/json" \
  -H "X-Delivery-ID: invalid-json-test" \
  -d '{"action":}')
echo "Response: $RESPONSE"
echo ""

# Test 5: Multiple webhooks to test processor failures
echo "🔄 Test 5: Sending multiple webhooks to test processor failures..."
for i in {1..5}; do
  DELIVERY_ID="batch-test-$i-$(date +%s)"
  echo "Sending webhook $i with delivery ID: $DELIVERY_ID"
  curl -s -X POST $WEBHOOK_URL \
    -H "Content-Type: application/json" \
    -H "X-Delivery-ID: $DELIVERY_ID" \
    -d "{\"action\": \"batch-test-$i\", \"data\": {\"test_number\": $i}}" > /dev/null
  sleep 1
done
echo "✅ Sent 5 webhooks. Check processor service logs for success/failure results."
echo ""

echo "🎉 Testing complete!"
echo "💡 To view logs: docker-compose logs -f"
echo "💡 To check Redis: docker exec -it \$(docker-compose ps -q redis) redis-cli"
