package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"time"

	"github.com/dapr/go-sdk/service/common"
	"processor-service/dlq"
)

type EventHandler struct {
	failureRate float64 // Probability of failure (0.25 = 25% = 1 in 4)
	dlqHandler  *dlq.DLQ<PERSON><PERSON>ler
}

type WebhookEvent struct {
	DeliveryID string                 `json:"delivery_id"`
	Timestamp  string                 `json:"timestamp"`
	Source     string                 `json:"source"`
	Payload    map[string]interface{} `json:"payload"`
}

func NewEventHandler(dlqHandler *dlq.DLQHandler) *EventHandler {
	// Seed random number generator
	rand.Seed(time.Now().UnixNano())

	return &EventHandler{
		failureRate: 0.25, // 1 in 4 requests will fail
		dlqHandler:  dlqHandler,
	}
}

// HandleWebhookEvent processes incoming webhook events from Dapr pubsub
func (h *EventHandler) HandleWebhookEvent(ctx context.Context, e *common.TopicEvent) (retry bool, err error) {
	log.Printf("Received webhook event from topic: %s", e.Topic)

	// Parse the event data
	var event WebhookEvent
	if err := json.Unmarshal(e.RawData, &event); err != nil {
		log.Printf("Error parsing webhook event: %v", err)
		return false, fmt.Errorf("failed to parse webhook event: %w", err)
	}

	log.Printf("Processing webhook event - Delivery ID: %s, Timestamp: %s, Source: %s",
		event.DeliveryID, event.Timestamp, event.Source)

	// Simulate processing time
	time.Sleep(100 * time.Millisecond)

	// Simulate random failures (1 in 4)
	if rand.Float64() < h.failureRate {
		log.Printf("Simulated failure for delivery ID: %s", event.DeliveryID)
		return true, fmt.Errorf("simulated processing failure for delivery ID: %s", event.DeliveryID)
	}

	// Log successful processing
	log.Printf("Successfully processed webhook event - Delivery ID: %s, Action: %v",
		event.DeliveryID, event.Payload["action"])

	return false, nil
}

// HandleWebhookHTTP handles direct HTTP calls to /events/webhook
func (h *EventHandler) HandleWebhookHTTP(ctx context.Context, in *common.InvocationEvent) (out *common.Content, err error) {
	log.Printf("Received direct HTTP webhook event")

	// Parse the event data
	var event WebhookEvent
	if err := json.Unmarshal(in.Data, &event); err != nil {
		log.Printf("Error parsing webhook event: %v", err)
		return &common.Content{
			Data:        []byte(`{"error": "Invalid JSON payload"}`),
			ContentType: "application/json",
		}, fmt.Errorf("invalid JSON payload: %w", err)
	}

	log.Printf("Processing HTTP webhook event - Delivery ID: %s", event.DeliveryID)

	// Simulate processing time
	time.Sleep(100 * time.Millisecond)

	// Simulate random failures (1 in 4)
	if rand.Float64() < h.failureRate {
		log.Printf("Simulated failure for delivery ID: %s", event.DeliveryID)
		return &common.Content{
			Data:        []byte(`{"error": "Simulated processing failure"}`),
			ContentType: "application/json",
		}, fmt.Errorf("simulated processing failure for delivery ID: %s", event.DeliveryID)
	}

	// Success response
	response := fmt.Sprintf(`{"status": "processed", "delivery_id": "%s", "timestamp": "%s"}`,
		event.DeliveryID, time.Now().Format(time.RFC3339))

	log.Printf("Successfully processed HTTP webhook event - Delivery ID: %s", event.DeliveryID)

	return &common.Content{
		Data:        []byte(response),
		ContentType: "application/json",
	}, nil
}

// HandleDLQEvent processes messages that have been moved to the dead letter queue
func (h *EventHandler) HandleDLQEvent(ctx context.Context, e *common.TopicEvent) (retry bool, err error) {
	log.Printf("Received DLQ event from topic: %s", e.Topic)

	// Parse the event data
	var event WebhookEvent
	if err := json.Unmarshal(e.RawData, &event); err != nil {
		log.Printf("Error parsing DLQ event: %v", err)
		return false, fmt.Errorf("failed to parse DLQ event: %w", err)
	}

	log.Printf("Processing DLQ event - Delivery ID: %s", event.DeliveryID)

	// Store the failed message in our DLQ storage for manual inspection
	failureReason := "Message failed after maximum retries"
	if err := h.dlqHandler.StoreDLQMessage(ctx, event.DeliveryID, event.Payload, failureReason); err != nil {
		log.Printf("Error storing DLQ message: %v", err)
		return false, fmt.Errorf("failed to store DLQ message: %w", err)
	}

	log.Printf("Successfully stored DLQ message - Delivery ID: %s", event.DeliveryID)
	return false, nil
}
