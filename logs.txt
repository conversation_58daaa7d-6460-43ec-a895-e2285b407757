2025-06-12 13:27:07 webhook-service-1         | 2025/06/12 11:27:07 New delivery ID saved: test-batch-1-20250612132707
2025-06-12 13:27:07 processor-service-1       | 2025/06/12 11:27:07 Received direct HTTP webhook event
2025-06-12 13:27:07 processor-service-1       | 2025/06/12 11:27:07 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:07 webhook-service-1         | 2025/06/12 11:27:07 Successfully published webhook event with delivery ID: test-batch-1-20250612132707
2025-06-12 13:27:07 processor-service-dapr-1  | time="2025-06-12T11:27:07.68728647Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: <nil>" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:07 processor-service-dapr-1  | time="2025-06-12T11:27:07.687352085Z" level=error msg="Error processing Redis message 1749727627585-0: unknown status returned from app while processing pub/sub event fca43073-c9ad-43fc-9e4c-9d28ca8d679d, status: processed, err: retriable error occurred" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:07 processor-service-1       | 2025/06/12 11:27:07 Successfully processed HTTP webhook event - Delivery ID: 
2025-06-12 13:27:08 webhook-service-1         | 2025/06/12 11:27:08 New delivery ID saved: test-batch-2-20250612132708
2025-06-12 13:27:08 webhook-service-1         | 2025/06/12 11:27:08 Successfully published webhook event with delivery ID: test-batch-2-20250612132708
2025-06-12 13:27:08 processor-service-1       | 2025/06/12 11:27:08 Received direct HTTP webhook event
2025-06-12 13:27:08 processor-service-1       | 2025/06/12 11:27:08 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:08 processor-service-dapr-1  | time="2025-06-12T11:27:08.767590458Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: <nil>" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:08 processor-service-dapr-1  | time="2025-06-12T11:27:08.767621419Z" level=error msg="Error processing Redis message 1749727628666-0: unknown status returned from app while processing pub/sub event dd73194d-f2e2-4577-8c22-4b3331f2b266, status: processed, err: retriable error occurred" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:08 processor-service-1       | 2025/06/12 11:27:08 Successfully processed HTTP webhook event - Delivery ID: 
2025-06-12 13:27:09 webhook-service-1         | 2025/06/12 11:27:09 New delivery ID saved: test-batch-3-20250612132709
2025-06-12 13:27:09 processor-service-1       | 2025/06/12 11:27:09 Received direct HTTP webhook event
2025-06-12 13:27:09 processor-service-1       | 2025/06/12 11:27:09 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:09 webhook-service-1         | 2025/06/12 11:27:09 Successfully published webhook event with delivery ID: test-batch-3-20250612132709
2025-06-12 13:27:09 processor-service-1       | 2025/06/12 11:27:09 Simulated failure for delivery ID: 
2025-06-12 13:27:09 processor-service-dapr-1  | time="2025-06-12T11:27:09.857319907Z" level=warning msg="retriable error returned from app while processing pub/sub event 9918f4bc-0b32-4b51-94b7-f89673063601, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:09 processor-service-dapr-1  | time="2025-06-12T11:27:09.85736017Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: retriable error returned from app while processing pub/sub event 9918f4bc-0b32-4b51-94b7-f89673063601, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:09 processor-service-dapr-1  | time="2025-06-12T11:27:09.857370005Z" level=error msg="Error processing Redis message 1749727629756-0: retriable error occurred: retriable error returned from app while processing pub/sub event 9918f4bc-0b32-4b51-94b7-f89673063601, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:10 webhook-service-1         | 2025/06/12 11:27:10 New delivery ID saved: test-batch-4-20250612132710
2025-06-12 13:27:10 webhook-service-1         | 2025/06/12 11:27:10 Successfully published webhook event with delivery ID: test-batch-4-20250612132710
2025-06-12 13:27:10 processor-service-1       | 2025/06/12 11:27:10 Received direct HTTP webhook event
2025-06-12 13:27:10 processor-service-1       | 2025/06/12 11:27:10 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:10 processor-service-dapr-1  | time="2025-06-12T11:27:10.9570114Z" level=warning msg="retriable error returned from app while processing pub/sub event 13fa5ab4-e012-4eb9-a3a6-22b9addf8a9c, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:10 processor-service-dapr-1  | time="2025-06-12T11:27:10.95705439Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: retriable error returned from app while processing pub/sub event 13fa5ab4-e012-4eb9-a3a6-22b9addf8a9c, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:10 processor-service-dapr-1  | time="2025-06-12T11:27:10.957066497Z" level=error msg="Error processing Redis message 1749727630855-0: retriable error occurred: retriable error returned from app while processing pub/sub event 13fa5ab4-e012-4eb9-a3a6-22b9addf8a9c, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:10 processor-service-1       | 2025/06/12 11:27:10 Simulated failure for delivery ID: 
2025-06-12 13:27:11 webhook-service-1         | 2025/06/12 11:27:11 New delivery ID saved: test-batch-5-20250612132711
2025-06-12 13:27:11 webhook-service-1         | 2025/06/12 11:27:11 Successfully published webhook event with delivery ID: test-batch-5-20250612132711
2025-06-12 13:27:11 processor-service-1       | 2025/06/12 11:27:11 Received direct HTTP webhook event
2025-06-12 13:27:11 processor-service-1       | 2025/06/12 11:27:11 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:12 processor-service-1       | 2025/06/12 11:27:12 Successfully processed HTTP webhook event - Delivery ID: 
2025-06-12 13:27:12 processor-service-dapr-1  | time="2025-06-12T11:27:12.039528403Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: <nil>" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:12 processor-service-dapr-1  | time="2025-06-12T11:27:12.03956399Z" level=error msg="Error processing Redis message 1749727631936-0: unknown status returned from app while processing pub/sub event 038f8249-f322-4fde-ab76-989ff69bc136, status: processed, err: retriable error occurred" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Received direct HTTP webhook event
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Received direct HTTP webhook event
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Received direct HTTP webhook event
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Received direct HTTP webhook event
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Received direct HTTP webhook event
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Simulated failure for delivery ID: 
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Successfully processed HTTP webhook event - Delivery ID: 
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Successfully processed HTTP webhook event - Delivery ID: 
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Simulated failure for delivery ID: 
2025-06-12 13:27:16 processor-service-1       | 2025/06/12 11:27:16 Successfully processed HTTP webhook event - Delivery ID: 
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.883853548Z" level=warning msg="retriable error returned from app while processing pub/sub event 90cdefa5-c8e9-49fc-8a94-927fd07a120a, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.883919307Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: retriable error returned from app while processing pub/sub event 90cdefa5-c8e9-49fc-8a94-927fd07a120a, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.88393475Z" level=error msg="Error processing Redis message 1749727441600-0: retriable error occurred: retriable error returned from app while processing pub/sub event 90cdefa5-c8e9-49fc-8a94-927fd07a120a, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.883955792Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: <nil>" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.88398201Z" level=error msg="Error processing Redis message 1749727438498-0: unknown status returned from app while processing pub/sub event fb633485-799b-47c3-ac3d-83eab5bd07b5, status: processed, err: retriable error occurred" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.883985662Z" level=warning msg="retriable error returned from app while processing pub/sub event b5591514-abf5-424e-83b0-d439b34c9267, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.883957522Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: <nil>" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.884103014Z" level=error msg="Error processing Redis message 1749727439553-0: unknown status returned from app while processing pub/sub event 2ecc856c-23e7-4e8e-bf0b-2a8affac4f4b, status: processed, err: retriable error occurred" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.884052341Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: retriable error returned from app while processing pub/sub event b5591514-abf5-424e-83b0-d439b34c9267, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.884204989Z" level=error msg="Error processing Redis message 1749727440575-0: retriable error occurred: retriable error returned from app while processing pub/sub event b5591514-abf5-424e-83b0-d439b34c9267, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.884145912Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: <nil>" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:16 processor-service-dapr-1  | time="2025-06-12T11:27:16.88428353Z" level=error msg="Error processing Redis message 1749727442630-0: unknown status returned from app while processing pub/sub event d4f77700-b436-49f4-9a7c-64b1285f6601, status: processed, err: retriable error occurred" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:24 redis-1                   | 1:M 12 Jun 2025 11:27:24.090 * 100 changes in 300 seconds. Saving...
2025-06-12 13:27:24 redis-1                   | 1:M 12 Jun 2025 11:27:24.091 * Background saving started by pid 21
2025-06-12 13:27:24 redis-1                   | 21:C 12 Jun 2025 11:27:24.100 * DB saved on disk
2025-06-12 13:27:24 redis-1                   | 21:C 12 Jun 2025 11:27:24.101 * Fork CoW for RDB: current 0 MB, peak 0 MB, average 0 MB
2025-06-12 13:27:24 redis-1                   | 1:M 12 Jun 2025 11:27:24.681 * Background saving terminated with success
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Received direct HTTP webhook event
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Received direct HTTP webhook event
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Received direct HTTP webhook event
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Received direct HTTP webhook event
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Received direct HTTP webhook event
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Received direct HTTP webhook event
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Processing HTTP webhook event - Delivery ID: 
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Simulated failure for delivery ID: 
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Successfully processed HTTP webhook event - Delivery ID: 
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Successfully processed HTTP webhook event - Delivery ID: 
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Successfully processed HTTP webhook event - Delivery ID: 
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Successfully processed HTTP webhook event - Delivery ID: 
2025-06-12 13:27:32 processor-service-1       | 2025/06/12 11:27:32 Successfully processed HTTP webhook event - Delivery ID: 
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.71613232Z" level=warning msg="retriable error returned from app while processing pub/sub event 4488069a-74b9-4714-9bfb-2a81e77088a8, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716186809Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: <nil>" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716218257Z" level=error msg="Error processing Redis message 1749727587598-0: unknown status returned from app while processing pub/sub event 5c8ced99-a3bb-4720-b224-62590cbee820, status: processed, err: retriable error occurred" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716245795Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: <nil>" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716268349Z" level=error msg="Error processing Redis message 1749727586476-0: unknown status returned from app while processing pub/sub event 012ca917-d61d-4ee1-8f2f-e17ce1811b80, status: processed, err: retriable error occurred" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716196602Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: retriable error returned from app while processing pub/sub event 4488069a-74b9-4714-9bfb-2a81e77088a8, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716457445Z" level=error msg="Error processing Redis message 1749727589749-0: retriable error occurred: retriable error returned from app while processing pub/sub event 4488069a-74b9-4714-9bfb-2a81e77088a8, topic: webhooks, body: simulated processing failure for delivery ID: \n. status code returned: 500" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716204399Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: <nil>" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716507089Z" level=error msg="Error processing Redis message 1749727588688-0: unknown status returned from app while processing pub/sub event 29a04761-b54f-44b7-8405-d62a4ba1e272, status: processed, err: retriable error occurred" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716388338Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: <nil>" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716556577Z" level=error msg="Error processing Redis message 1749727590818-0: unknown status returned from app while processing pub/sub event 721472cf-5634-4dd1-b7e3-a2014b92b46e, status: processed, err: retriable error occurred" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716384487Z" level=warning msg="encountered a retriable error while publishing a subscribed message to topic webhooks, err: <nil>" app_id=processor-service instance=5297006a8142 scope=dapr.runtime.processor.pubsub type=log ver=1.12.0
2025-06-12 13:27:32 processor-service-dapr-1  | time="2025-06-12T11:27:32.716594769Z" level=error msg="Error processing Redis message 1749727396199-0: unknown status returned from app while processing pub/sub event 4103fd62-b968-4a7f-86df-c9ab15e46d57, status: processed, err: retriable error occurred" app_id=processor-service component="redisstreams-pubsub (pubsub.redis/v1)" instance=5297006a8142 scope=dapr.contrib type=log ver=1.12.0
