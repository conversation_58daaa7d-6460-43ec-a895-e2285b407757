graph TB
    subgraph "Webhook Processing Flow"
        Client[🌐 Client] --> WS[🎯 Webhook Service<br/>Port 8080]
        WS --> Redis[(🗄️ Redis<br/>State + PubSub)]
        Redis --> PS[⚙️ Processor Service<br/>Port 8081]
    end
    
    subgraph "Retry & DLQ Flow"
        PS --> Success{✅ Success?}
        Success -->|Yes| Complete[✅ Complete]
        Success -->|No| Retry{🔄 Retry Count<br/>< Max?}
        Retry -->|Yes| PS
        Retry -->|No| DLQTopic[📮 DLQ Topic<br/>webhooks-dlq]
        DLQTopic --> DLQHandler[🗂️ DLQ Handler]
        DLQHandler --> DLQStorage[(🗄️ DLQ Storage<br/>Redis State)]
    end
    
    subgraph "DLQ Management"
        DLQServer[🔧 DLQ Server<br/>Port 8082]
        DLQServer --> DLQStorage
        Admin[👤 Admin] --> DLQServer
        
        DLQServer --> |GET /dlq| ListDLQ[📋 List DLQ Messages]
        DLQServer --> |GET /dlq/stats| Stats[📊 DLQ Statistics]
        DLQServer --> |GET /dlq/{id}| GetDLQ[🔍 Get DLQ Message]
        DLQServer --> |DELETE /dlq/{id}| DeleteDLQ[🗑️ Delete DLQ Message]
        DLQServer --> |POST /dlq/{id}/reprocess| Reprocess[🔄 Reprocess DLQ Message]
    end
    
    Reprocess --> Redis
    
    style DLQTopic fill:#ffcccc
    style DLQStorage fill:#ffcccc
    style DLQServer fill:#ccffcc