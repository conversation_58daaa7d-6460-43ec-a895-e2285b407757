package dlq

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	dapr "github.com/dapr/go-sdk/client"
)

// DLQMessage represents a message in the dead letter queue
type DLQMessage struct {
	ID            string                 `json:"id"`
	OriginalTopic string                 `json:"original_topic"`
	DeliveryID    string                 `json:"delivery_id"`
	Payload       map[string]interface{} `json:"payload"`
	FailureReason string                 `json:"failure_reason"`
	FailureCount  int                    `json:"failure_count"`
	FirstFailure  time.Time              `json:"first_failure"`
	LastFailure   time.Time              `json:"last_failure"`
	CreatedAt     time.Time              `json:"created_at"`
}

// DLQHandler manages dead letter queue operations
type DLQHandler struct {
	daprClient dapr.Client
	stateStore string
}

// NewDLQHandler creates a new DLQ handler
func NewDLQHandler(daprClient dapr.Client) *DLQHandler {
	return &DLQHandler{
		daprClient: daprClient,
		stateStore: "redis",
	}
}

// StoreDLQMessage stores a failed message in the DLQ
func (h *DLQHandler) StoreDLQMessage(ctx context.Context, deliveryID string, originalPayload map[string]interface{}, failureReason string) error {
	dlqID := fmt.Sprintf("dlq_%s_%d", deliveryID, time.Now().Unix())
	
	dlqMessage := DLQMessage{
		ID:            dlqID,
		OriginalTopic: "webhooks",
		DeliveryID:    deliveryID,
		Payload:       originalPayload,
		FailureReason: failureReason,
		FailureCount:  1,
		FirstFailure:  time.Now(),
		LastFailure:   time.Now(),
		CreatedAt:     time.Now(),
	}

	// Check if this delivery ID already exists in DLQ
	existingKey := fmt.Sprintf("dlq_delivery_%s", deliveryID)
	existingItem, err := h.daprClient.GetState(ctx, h.stateStore, existingKey, nil)
	if err == nil && existingItem.Value != nil {
		// Update existing DLQ message
		var existing DLQMessage
		if err := json.Unmarshal(existingItem.Value, &existing); err == nil {
			dlqMessage.FailureCount = existing.FailureCount + 1
			dlqMessage.FirstFailure = existing.FirstFailure
			dlqMessage.ID = existing.ID
		}
	}

	// Store the DLQ message
	dlqData, err := json.Marshal(dlqMessage)
	if err != nil {
		return fmt.Errorf("failed to marshal DLQ message: %w", err)
	}

	// Store with both the unique ID and delivery ID for easy lookup
	if err := h.daprClient.SaveState(ctx, h.stateStore, dlqMessage.ID, dlqData, nil); err != nil {
		return fmt.Errorf("failed to save DLQ message: %w", err)
	}

	if err := h.daprClient.SaveState(ctx, h.stateStore, existingKey, dlqData, nil); err != nil {
		return fmt.Errorf("failed to save DLQ delivery mapping: %w", err)
	}

	log.Printf("Stored message in DLQ: delivery_id=%s, dlq_id=%s, failure_count=%d", 
		deliveryID, dlqMessage.ID, dlqMessage.FailureCount)

	return nil
}

// GetDLQMessage retrieves a DLQ message by delivery ID
func (h *DLQHandler) GetDLQMessage(ctx context.Context, deliveryID string) (*DLQMessage, error) {
	key := fmt.Sprintf("dlq_delivery_%s", deliveryID)
	item, err := h.daprClient.GetState(ctx, h.stateStore, key, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get DLQ message: %w", err)
	}

	if item.Value == nil {
		return nil, nil // Not found
	}

	var dlqMessage DLQMessage
	if err := json.Unmarshal(item.Value, &dlqMessage); err != nil {
		return nil, fmt.Errorf("failed to unmarshal DLQ message: %w", err)
	}

	return &dlqMessage, nil
}

// ListDLQMessages returns all DLQ messages (simplified implementation)
func (h *DLQHandler) ListDLQMessages(ctx context.Context) ([]DLQMessage, error) {
	// Note: This is a simplified implementation. In production, you'd want
	// to use a proper query mechanism or maintain an index
	
	// For now, we'll return a placeholder implementation
	// In a real system, you'd query Redis with a pattern like "dlq_*"
	return []DLQMessage{}, nil
}

// ReprocessDLQMessage attempts to reprocess a message from the DLQ
func (h *DLQHandler) ReprocessDLQMessage(ctx context.Context, deliveryID string) error {
	dlqMessage, err := h.GetDLQMessage(ctx, deliveryID)
	if err != nil {
		return fmt.Errorf("failed to get DLQ message: %w", err)
	}

	if dlqMessage == nil {
		return fmt.Errorf("DLQ message not found for delivery ID: %s", deliveryID)
	}

	// Republish the message to the original topic
	if err := h.daprClient.PublishEvent(ctx, "redisstreams-pubsub", "webhooks", dlqMessage.Payload); err != nil {
		return fmt.Errorf("failed to republish DLQ message: %w", err)
	}

	log.Printf("Reprocessed DLQ message: delivery_id=%s", deliveryID)
	return nil
}

// DeleteDLQMessage removes a message from the DLQ
func (h *DLQHandler) DeleteDLQMessage(ctx context.Context, deliveryID string) error {
	dlqMessage, err := h.GetDLQMessage(ctx, deliveryID)
	if err != nil {
		return fmt.Errorf("failed to get DLQ message: %w", err)
	}

	if dlqMessage == nil {
		return fmt.Errorf("DLQ message not found for delivery ID: %s", deliveryID)
	}

	// Delete both the unique ID and delivery ID mappings
	if err := h.daprClient.DeleteState(ctx, h.stateStore, dlqMessage.ID, nil); err != nil {
		return fmt.Errorf("failed to delete DLQ message: %w", err)
	}

	deliveryKey := fmt.Sprintf("dlq_delivery_%s", deliveryID)
	if err := h.daprClient.DeleteState(ctx, h.stateStore, deliveryKey, nil); err != nil {
		return fmt.Errorf("failed to delete DLQ delivery mapping: %w", err)
	}

	log.Printf("Deleted DLQ message: delivery_id=%s", deliveryID)
	return nil
}
