package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"time"

	"github.com/dapr/go-sdk/service/common"
)

type EventHandler struct {
	failureRate float64 // Probability of failure (0.25 = 25% = 1 in 4)
}

type WebhookEvent struct {
	DeliveryID string                 `json:"delivery_id"`
	Timestamp  string                 `json:"timestamp"`
	Source     string                 `json:"source"`
	Payload    map[string]interface{} `json:"payload"`
}

func NewEventHandler() *EventHandler {
	// Seed random number generator
	rand.Seed(time.Now().UnixNano())
	
	return &EventHandler{
		failureRate: 0.25, // 1 in 4 requests will fail
	}
}

// HandleWebhookEvent processes incoming webhook events from Dapr pubsub
func (h *EventHandler) HandleWebhookEvent(ctx *common.TopicEvent) (retry bool, err error) {
	log.Printf("Received webhook event from topic: %s", ctx.Topic)

	// Parse the event data
	var event WebhookEvent
	if err := json.Unmarshal(ctx.RawData, &event); err != nil {
		log.Printf("Error parsing webhook event: %v", err)
		return false, fmt.Errorf("failed to parse webhook event: %w", err)
	}

	log.Printf("Processing webhook event - Delivery ID: %s, Timestamp: %s, Source: %s", 
		event.DeliveryID, event.Timestamp, event.Source)

	// Simulate processing time
	time.Sleep(100 * time.Millisecond)

	// Simulate random failures (1 in 4)
	if rand.Float64() < h.failureRate {
		log.Printf("Simulated failure for delivery ID: %s", event.DeliveryID)
		return true, fmt.Errorf("simulated processing failure for delivery ID: %s", event.DeliveryID)
	}

	// Log successful processing
	log.Printf("Successfully processed webhook event - Delivery ID: %s, Action: %v", 
		event.DeliveryID, event.Payload["action"])

	return false, nil
}

// HandleWebhookHTTP handles direct HTTP calls to /events/webhook
func (h *EventHandler) HandleWebhookHTTP(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	log.Printf("Received direct HTTP webhook event")

	// Parse the event data
	var event WebhookEvent
	if err := json.NewDecoder(r.Body).Decode(&event); err != nil {
		log.Printf("Error parsing webhook event: %v", err)
		http.Error(w, "Invalid JSON payload", http.StatusBadRequest)
		return
	}

	log.Printf("Processing HTTP webhook event - Delivery ID: %s", event.DeliveryID)

	// Simulate processing time
	time.Sleep(100 * time.Millisecond)

	// Simulate random failures (1 in 4)
	if rand.Float64() < h.failureRate {
		log.Printf("Simulated failure for delivery ID: %s", event.DeliveryID)
		http.Error(w, "Simulated processing failure", http.StatusInternalServerError)
		return
	}

	// Success response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"status": "processed", "delivery_id": "%s", "timestamp": "%s"}`, 
		event.DeliveryID, time.Now().Format(time.RFC3339))

	log.Printf("Successfully processed HTTP webhook event - Delivery ID: %s", event.DeliveryID)
}
