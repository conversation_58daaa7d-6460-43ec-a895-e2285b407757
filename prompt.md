You are to generate a minimal Go server with two services:
1. A **Webhook Receiver** microservice
2. A **Processor** microservice

The purpose is to simulate a Dapr-powered event processing flow using Redis Streams for pub/sub and state store.

---

## 🔧 Tech Stack

- Language: Go
- Framework: Standard `net/http`
- Containerized with Docker
- Use Dapr sidecars for both services
- Redis as:
  - Dapr pubsub component (`redisstreams`)
  - Dapr state store component (`redis`)
- Use Dapr CLI for local dev

---

## 🧩 Service 1: Webhook Receiver

**Responsibilities:**

- Expose `POST /webhook` endpoint
- Accept JSON payloads (simulate GitHub or Jira webhooks)
- Validate payload format (simple schema check)
- Deduplicate using Dapr state store:
  - Use `X-Delivery-ID` header as unique key
  - Save it with a short TTL
  - If duplicate, return 200 OK with log
- On success, publish event to topic `webhooks` via Dapr pubsub
- Return `200 OK` on success, `500` on publish error

**Files:**
- `main.go`
- `handlers/webhook.go`
- `pubsub/publisher.go`
- `dedup/deduplicator.go`

**Ports:**
- App: 8080
- Dapr: 3500 (default)

---

## 🧩 Service 2: Processor Microservice

**Responsibilities:**

- Subscribe to topic `webhooks` using Dapr
- Expose route `/events/webhook` for POST
- Log incoming payloads
- Simulate 1 out of 4 failures (random) to test retries
- Return `200 OK` on success, `500` on failure

**Files:**
- `main.go`
- `handlers/event_handler.go`

---

## 📦 Docker Setup

Create two Dockerfiles and a `docker-compose.yml` with:
- Redis
- Webhook service
- Processor service
- Dapr sidecars (use CLI or init with compose)

---

## 📁 Directory Structure

```
project-root/
├── webhook-service/
│ ├── main.go
│ ├── handlers/
│ ├── pubsub/
│ └── dedup/
├── processor-service/
│ ├── main.go
│ └── handlers/
├── components/
│ ├── pubsub.yaml
│ └── statestore.yaml
├── docker-compose.yml
└── README.md
```


---

## 🔧 Dapr Components

1. **pubsub.yaml**
```yaml
apiVersion: dapr.io/v1alpha1
kind: Component
metadata:
  name: redisstreams-pubsub
spec:
  type: pubsub.redisstreams
  version: v1
  metadata:
  - name: redisHost
    value: redis:6379
  - name: consumerID
    value: webhook-processor
  - name: enableTLS
    value: "false"
```

2. **statestore.yaml**

```yaml
apiVersion: dapr.io/v1alpha1
kind: Component
metadata:
  name: redis
spec:
  type: state.redis
  version: v1
  metadata:
  - name: redisHost
    value: redis:6379
  - name: enableTLS
    value: "false"

```

## ✅ Goals

- Run both services with Dapr

- Test publishing a webhook via curl or Postman

- Validate deduplication

- Confirm delivery to processor

- Trigger retries and DLQ by simulating errors

Optional: observe Redis streams and state via CLI or dashboard

---