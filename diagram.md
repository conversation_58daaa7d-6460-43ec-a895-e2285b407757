sequenceDiagram
    participant Client as 🌐 Client (curl/PowerShell)
    participant WH as 🎯 Webhook Service<br/>(Port 8080)
    participant WHD as 🔧 Webhook Dapr Sidecar<br/>(Port 3500)
    participant Redis as 🗄️ Redis<br/>(State + PubSub)
    participant PD as 🔧 Processor Dapr Sidecar<br/>(Port 3501)
    participant PS as ⚙️ Processor Service<br/>(Port 8081)

    Note over Client,PS: 1. WEBHOOK RECEPTION & DEDUPLICATION
    Client->>WH: POST /webhook<br/>X-Delivery-ID: test-batch-1-20250612132707<br/>{"action": "batch-test-1"}
    
    WH->>WHD: Check if delivery ID exists<br/>(Dapr State Store API)
    WHD->>Redis: GET webhook_delivery_test-batch-1-20250612132707
    Redis-->>WHD: Key not found (new delivery)
    WHD-->>WH: Not duplicate
    
    WH->>WHD: Save delivery ID with TTL<br/>(Dapr State Store API)
    WHD->>Redis: SET webhook_delivery_test-batch-1-20250612132707<br/>TTL: 300 seconds
    Redis-->>WHD: OK
    WHD-->>WH: Saved
    
    Note over WH: 📝 Log: "New delivery ID saved: test-batch-1-20250612132707"
    
    Note over Client,PS: 2. EVENT PUBLISHING
    WH->>WHD: Publish event to topic 'webhooks'<br/>(Dapr PubSub API)
    WHD->>Redis: XADD webhooks * {event_data}
    Redis-->>WHD: Message ID: 1749727627585-0
    WHD-->>WH: Published
    
    Note over WH: 📝 Log: "Successfully published webhook event with delivery ID: test-batch-1-20250612132707"
    
    WH-->>Client: 200 OK<br/>{"status": "success", "delivery_id": "test-batch-1-20250612132707"}
    
    Note over Client,PS: 3. EVENT CONSUMPTION & PROCESSING
    Redis->>PD: XREAD webhooks (Redis Streams)<br/>Message: 1749727627585-0
    PD->>PS: POST /events/webhook<br/>{webhook_event_data}
    
    Note over PS: 📝 Log: "Received direct HTTP webhook event"
    Note over PS: 📝 Log: "Processing HTTP webhook event - Delivery ID: "
    Note over PS: 🎲 Random failure check (25% chance)
    
    alt Success Case (75% probability)
        Note over PS: ✅ Processing succeeds
        Note over PS: 📝 Log: "Successfully processed HTTP webhook event"
        PS-->>PD: 200 OK {"status": "processed"}
        PD->>Redis: ACK message 1749727627585-0
    else Failure Case (25% probability)
        Note over PS: ❌ Simulated failure
        Note over PS: 📝 Log: "Simulated failure for delivery ID: "
        PS-->>PD: 500 Internal Server Error
        Note over PD: 📝 Log: "retriable error returned from app"
        Note over PD: 🔄 Schedule retry with exponential backoff
        PD->>Redis: NACK message (keep in stream for retry)
    end
    
    Note over Client,PS: 4. RETRY MECHANISM (for failed events)
    Note over PD: ⏰ Wait for retry interval (exponential backoff)
    PD->>PS: POST /events/webhook (RETRY)<br/>{same_webhook_event_data}
    Note over PS: 📝 Log: "Received direct HTTP webhook event" (again)
    Note over PS: 🎲 Random failure check again (25% chance)
    
    alt Retry Success
        PS-->>PD: 200 OK
        PD->>Redis: ACK message
        Note over PD: ✅ Event finally processed
    else Retry Failure
        PS-->>PD: 500 Error
        Note over PD: 🔄 Schedule another retry
    end