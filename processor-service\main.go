package main

import (
	"context"
	"log"
	"net/http"

	dapr "github.com/dapr/go-sdk/client"
	"github.com/dapr/go-sdk/service/common"
	daprd "github.com/dapr/go-sdk/service/http"
	"processor-service/dlq"
	"processor-service/handlers"
	"processor-service/server"
)

const (
	ServerPort   = ":8081"
	DLQPort      = ":8082"
	PubSubName   = "redisstreams-pubsub"
	TopicName    = "webhooks"
	DLQTopicName = "webhooks-dlq"
)

func main() {
	log.Println("Starting Processor Service...")

	// Create Dapr client for DLQ operations
	daprClient, err := dapr.NewClient()
	if err != nil {
		log.Fatalf("Error creating Dapr client: %v", err)
	}
	defer daprClient.Close()

	// Create DLQ handler
	dlqHandler := dlq.NewDLQHandler(daprClient)

	// Create event handler with DLQ support
	eventHandler := handlers.NewEventHandler(dlqHandler)

	// Create DLQ HTTP handlers
	dlqHandlers := handlers.NewDLQHandlers(dlqHandler)

	// Create DLQ HTTP server
	dlqServer := server.NewDLQServer(dlqHandlers)

	// Start DLQ server in a goroutine
	go func() {
		if err := dlqServer.Start(DLQPort); err != nil {
			log.Fatalf("Error starting DLQ server: %v", err)
		}
	}()

	// Create Dapr service
	s := daprd.NewService(ServerPort)

	// Add topic subscription for main webhook events
	if err := s.AddTopicEventHandler(&common.Subscription{
		PubsubName: PubSubName,
		Topic:      TopicName,
		Route:      "/events/webhook",
	}, eventHandler.HandleWebhookEvent); err != nil {
		log.Fatalf("Error adding topic event handler: %v", err)
	}

	// Add topic subscription for DLQ events
	if err := s.AddTopicEventHandler(&common.Subscription{
		PubsubName: PubSubName,
		Topic:      DLQTopicName,
		Route:      "/events/dlq",
	}, eventHandler.HandleDLQEvent); err != nil {
		log.Fatalf("Error adding DLQ topic event handler: %v", err)
	}

	// Add HTTP endpoint for direct calls
	if err := s.AddServiceInvocationHandler("/events/webhook", eventHandler.HandleWebhookHTTP); err != nil {
		log.Fatalf("Error adding service invocation handler: %v", err)
	}

	// Add health check endpoint
	if err := s.AddServiceInvocationHandler("/health", func(ctx context.Context, in *common.InvocationEvent) (out *common.Content, err error) {
		return &common.Content{
			Data:        []byte("OK"),
			ContentType: "text/plain",
		}, nil
	}); err != nil {
		log.Fatalf("Error adding health check handler: %v", err)
	}

	log.Printf("Processor service listening on %s", ServerPort)
	log.Printf("DLQ management server listening on %s", DLQPort)
	log.Printf("Subscribed to topic '%s' on pubsub '%s'", TopicName, PubSubName)
	log.Printf("Subscribed to DLQ topic '%s' on pubsub '%s'", DLQTopicName, PubSubName)

	// Start the service
	if err := s.Start(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Error starting service: %v", err)
	}
}
