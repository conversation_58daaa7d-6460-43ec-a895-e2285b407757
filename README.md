# Minimal Webhook Microservice with Dapr

A minimal Go-based microservice architecture demonstrating webhook processing using Dapr for pub/sub messaging and state management.

## 🏗️ Architecture

- **Webhook Service**: Receives webhooks, deduplicates using Redis state store, publishes to Redis Streams
- **Processor Service**: Subscribes to webhook events, processes them with simulated failures
- **Redis**: Used for both Dapr state store (deduplication) and pub/sub (Redis Streams)
- **Dapr**: Provides abstraction for state management and pub/sub messaging

## 📁 Project Structure

```
├── webhook-service/          # Webhook receiver microservice
│   ├── main.go              # HTTP server and routes
│   ├── handlers/            # HTTP handlers
│   ├── pubsub/              # Dapr pubsub publisher
│   ├── dedup/               # Deduplication logic
│   └── Dockerfile           # Container configuration
├── processor-service/        # Event processor microservice
│   ├── main.go              # Dapr service with subscriptions
│   ├── handlers/            # Event processing handlers
│   └── Dockerfile           # Container configuration
├── components/              # Dapr component configurations
│   ├── pubsub.yaml          # Redis Streams pubsub
│   └── statestore.yaml      # Redis state store
└── docker-compose.yml       # Multi-service setup
```

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Go 1.21+ (for local development)
- Dapr CLI (optional, for local development)

### Running with Docker Compose

1. **Start all services:**
   ```bash
   docker-compose up --build
   ```

2. **Test webhook endpoint:**
   ```bash
   curl -X POST http://localhost:8080/webhook \
     -H "Content-Type: application/json" \
     -H "X-Delivery-ID: test-123" \
     -d '{
       "action": "opened",
       "repository": {
         "name": "test-repo",
         "full_name": "user/test-repo"
       },
       "issue": {
         "number": 1,
         "title": "Test Issue"
       }
     }'
   ```

3. **Test deduplication (send same request again):**
   ```bash
   # Same curl command as above - should return duplicate status
   ```

4. **Check health endpoints:**
   ```bash
   curl http://localhost:8080/health  # Webhook service
   curl http://localhost:8081/health  # Processor service
   ```

### Running Locally with Dapr CLI

1. **Start Redis:**
   ```bash
   docker run -d --name redis -p 6379:6379 redis:7-alpine
   ```

2. **Run Webhook Service:**
   ```bash
   cd webhook-service
   go mod tidy
   dapr run --app-id webhook-service --app-port 8080 --dapr-http-port 3500 --components-path ../components go run main.go
   ```

3. **Run Processor Service (in another terminal):**
   ```bash
   cd processor-service
   go mod tidy
   dapr run --app-id processor-service --app-port 8081 --dapr-http-port 3501 --components-path ../components go run main.go
   ```

## 🧪 Testing

### Test Webhook Processing

```bash
# Send a webhook
curl -X POST http://localhost:8080/webhook \
  -H "Content-Type: application/json" \
  -H "X-Delivery-ID: unique-$(date +%s)" \
  -d '{
    "action": "push",
    "repository": {"name": "my-repo"},
    "data": {"commits": 3}
  }'
```

### Test Deduplication

```bash
# Send the same delivery ID twice
DELIVERY_ID="test-dedup-123"

curl -X POST http://localhost:8080/webhook \
  -H "Content-Type: application/json" \
  -H "X-Delivery-ID: $DELIVERY_ID" \
  -d '{"action": "test"}'

# Second request should return duplicate status
curl -X POST http://localhost:8080/webhook \
  -H "Content-Type: application/json" \
  -H "X-Delivery-ID: $DELIVERY_ID" \
  -d '{"action": "test"}'
```

### Observe Processing Failures

The processor service simulates a 25% failure rate. Send multiple requests to see both successes and failures:

```bash
for i in {1..10}; do
  curl -X POST http://localhost:8080/webhook \
    -H "Content-Type: application/json" \
    -H "X-Delivery-ID: test-$i" \
    -d "{\"action\": \"test-$i\"}"
  echo ""
done
```

## 📊 Monitoring

### View Logs

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f webhook-service
docker-compose logs -f processor-service
```

### Redis Inspection

```bash
# Connect to Redis
docker exec -it $(docker-compose ps -q redis) redis-cli

# View state store keys (deduplication)
KEYS webhook_delivery_*

# View streams (pubsub)
XINFO GROUPS webhooks
XLEN webhooks
```

## 🔧 Configuration

### Environment Variables

- `DAPR_HTTP_PORT`: Dapr HTTP port (default: 3500 for webhook, 3501 for processor)
- `DAPR_GRPC_PORT`: Dapr gRPC port (default: 50001 for webhook, 50002 for processor)

### Dapr Components

- **State Store**: Redis with 5-minute TTL for deduplication
- **Pub/Sub**: Redis Streams with consumer group "webhook-processor"

## 🛠️ Development

### Adding New Event Types

1. Update `WebhookPayload` struct in `webhook-service/handlers/webhook.go`
2. Add validation logic as needed
3. Update `WebhookEvent` struct in both services if needed

### Adjusting Failure Rate

Modify `failureRate` in `processor-service/handlers/event_handler.go`:

```go
failureRate: 0.25, // 25% failure rate (1 in 4)
```

### Changing TTL for Deduplication

Update `TTLSeconds` in `webhook-service/dedup/deduplicator.go`:

```go
TTLSeconds = 300 // 5 minutes
```

## 🔍 Troubleshooting

### Common Issues

1. **Services not starting**: Check if ports 8080, 8081, 6379 are available
2. **Dapr connection errors**: Ensure Redis is running and accessible
3. **Events not processing**: Check processor service logs for subscription errors

### Debug Commands

```bash
# Check service health
curl http://localhost:8080/health
curl http://localhost:8081/health

# View Dapr metadata
curl http://localhost:3500/v1.0/metadata
curl http://localhost:3501/v1.0/metadata

# Check Redis connectivity
docker exec -it $(docker-compose ps -q redis) redis-cli ping
```
